<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tooltip Fix Test</title>
    <link rel="stylesheet" href="snapapp.css">
    <link rel="stylesheet" href="components/charts/snap-charts.css">
</head>
<body>
    <div class="main-content" data-theme="dark">
        <div style="padding: 20px;">
            <h2>Daily Sales History Tooltip Test</h2>
            <p>Hover over the rightmost columns to test the tooltip wrapping fix.</p>
            
            <div id="test-chart" style="width: 600px; height: 400px; border: 1px solid #ccc; margin: 20px 0;"></div>
            
            <div style="margin-top: 20px;">
                <h3>Test Instructions:</h3>
                <ol>
                    <li>Hover over the rightmost columns in the chart</li>
                    <li>Check that the returns value and percentage stay on one line</li>
                    <li>Verify that tooltips near the edge don't wrap text</li>
                </ol>
            </div>
        </div>
    </div>

    <script src="components/charts/snap-charts.js"></script>
    <script>
        // Create test data with returns that would cause wrapping
        const testData = [
            { month: 'JUL', day: '26', year: '25', sales: 300, royalties: 100, returns: 12 },
            { month: 'JUL', day: '27', year: '25', sales: 250, royalties: 85, returns: 8 },
            { month: 'JUL', day: '28', year: '25', sales: 319, royalties: 110, returns: 15 }, // This should show "(-15) 4.70%"
            { month: 'JUL', day: '29', year: '25', sales: 168, royalties: 29, returns: 0 },
            { month: 'JUL', day: '30', year: '25', sales: 280, royalties: 95, returns: 18 }
        ];

        // Create chart instance
        const chart = new SnapChart({
            container: '#test-chart',
            type: 'daily-sales-history',
            data: testData,
            showContainer: true,
            showTitle: false,
            showDataEditor: false,
            showControls: false,
            showInsights: false,
            options: {
                title: 'Daily Sales History Test',
                subtitle: 'Testing tooltip wrapping fix'
            }
        });

        console.log('Test chart created with data:', testData);
    </script>
</body>
</html>
