<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Returns Calculation Fix Test</title>
    <link rel="stylesheet" href="components/charts/snap-charts.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        .test-description {
            color: #666;
            margin-bottom: 15px;
            font-size: 14px;
        }
        .chart-container {
            height: 300px;
            margin: 20px 0;
        }
        .debug-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 12px;
            border-left: 4px solid #007bff;
        }
        .debug-info h4 {
            margin: 0 0 10px 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            font-size: 14px;
            color: #333;
        }
        .expected {
            color: #28a745;
            font-weight: 600;
        }
        .actual {
            color: #dc3545;
            font-weight: 600;
        }
        .fixed {
            color: #007bff;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <h1>Returns Calculation Fix Test</h1>
    <p>This test demonstrates the fix for the returns calculation discrepancy in comparison mode tooltips.</p>

    <div class="test-container">
        <div class="test-title">Test Case: Returns Total Mismatch</div>
        <div class="test-description">
            This test creates comparison data where the provided total returns doesn't match the sum of individual marketplace returns.
            The fix ensures the tooltip shows consistent values.
        </div>
        <div id="testChart" class="chart-container"></div>
        <div class="debug-info">
            <h4>Debug Information:</h4>
            <div id="debugOutput">Loading test data...</div>
        </div>
    </div>

    <script src="components/charts/snap-charts.js"></script>
    <script>
        // Create test data with intentional discrepancy
        function createTestData() {
            const currentData = [{
                month: 'SEP',
                day: '25',
                year: '25',
                marketplaces: [
                    { code: 'US', sales: 640, royalties: 174, returns: 0 },
                    { code: 'UK', sales: 160, royalties: 44, returns: 3 },
                    { code: 'DE', sales: 0, royalties: 0, returns: 0 },
                    { code: 'FR', sales: 0, royalties: 0, returns: 0 },
                    { code: 'IT', sales: 0, royalties: 0, returns: 0 }
                ],
                sales: 800,
                royalties: 218,
                returns: 3, // Correct total
                values: [640, 160, 0, 0, 0],
                labels: ['US', 'UK', 'DE', 'FR', 'IT']
            }];

            const comparisonData = [{
                month: 'SEP',
                day: '24',
                year: '24',
                marketplaces: [
                    { code: 'US', sales: 529, royalties: 157, returns: 0 },
                    { code: 'UK', sales: 472, royalties: 140, returns: 41 },
                    { code: 'DE', sales: 280, royalties: 83, returns: 24 },
                    { code: 'FR', sales: 559, royalties: 166, returns: 48 },
                    { code: 'IT', sales: 0, royalties: 0, returns: 0 }
                ],
                sales: 1840,
                royalties: 545,
                returns: 158, // INTENTIONALLY WRONG - should be 113 (0+41+24+48+0)
                values: [529, 472, 280, 559, 0],
                labels: ['US', 'UK', 'DE', 'FR', 'IT']
            }];

            return { currentData, comparisonData };
        }

        // Calculate expected values
        function calculateExpectedValues(data) {
            const calculatedReturns = data.marketplaces.reduce((sum, mp) => sum + (mp.returns || 0), 0);
            return {
                provided: data.returns,
                calculated: calculatedReturns,
                discrepancy: data.returns - calculatedReturns
            };
        }

        // Initialize test
        function runTest() {
            const testData = createTestData();
            const currentExpected = calculateExpectedValues(testData.currentData[0]);
            const comparisonExpected = calculateExpectedValues(testData.comparisonData[0]);

            // Display debug information
            const debugOutput = document.getElementById('debugOutput');
            debugOutput.innerHTML = `
                <strong>Current Data (SEP '25):</strong><br>
                Provided total returns: <span class="expected">${currentExpected.provided}</span><br>
                Calculated from marketplaces: <span class="expected">${currentExpected.calculated}</span><br>
                Discrepancy: ${currentExpected.discrepancy}<br><br>
                
                <strong>Comparison Data (SEP '24) - THE ISSUE:</strong><br>
                Provided total returns: <span class="actual">${comparisonExpected.provided}</span><br>
                Calculated from marketplaces: <span class="fixed">${comparisonExpected.calculated}</span><br>
                Discrepancy: <span class="actual">${comparisonExpected.discrepancy}</span><br><br>
                
                <strong>Expected Fix:</strong><br>
                The tooltip should show <span class="fixed">${comparisonExpected.calculated}</span> for the total returns in the comparison column,<br>
                matching the sum of individual marketplace returns: 0 + 41 + 24 + 48 + 0 = ${comparisonExpected.calculated}<br><br>
                
                <strong>Instructions:</strong><br>
                1. Hover over the comparison column (right side)<br>
                2. Check that the "Returns" total matches the sum of individual marketplace returns<br>
                3. Look for console warnings about discrepancies
            `;

            // Create chart
            const chart = new SnapChart({
                container: '#testChart',
                type: 'stacked-column',
                data: testData.currentData,
                options: {
                    title: 'Returns Fix Test Chart',
                    compareMode: true,
                    compareData: testData.comparisonData,
                    animate: true,
                    responsive: true
                }
            });

            // Monitor console for debug messages
            const originalWarn = console.warn;
            console.warn = function(...args) {
                if (args[0] && args[0].includes && args[0].includes('Returns total discrepancy')) {
                    debugOutput.innerHTML += `<br><strong style="color: #ff6b35;">Console Warning Detected:</strong><br>${JSON.stringify(args[1], null, 2)}`;
                }
                originalWarn.apply(console, args);
            };
        }

        // Run test when page loads
        document.addEventListener('DOMContentLoaded', runTest);
    </script>
</body>
</html>
