<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tooltip & Royalties Line Fixes Test</title>
    <link rel="stylesheet" href="components/charts/snap-charts.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        .test-description {
            color: #666;
            margin-bottom: 15px;
            font-size: 14px;
        }
        .chart-container {
            height: 400px;
            margin: 20px 0;
        }
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
            border-left: 4px solid #2196f3;
        }
        .instructions h4 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        .instructions ol {
            margin: 0;
            padding-left: 20px;
        }
        .instructions li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <h1>Tooltip & Royalties Line Fixes Test</h1>
    <p>This test verifies the fixes for tooltip z-index and royalties line rendering order issues.</p>

    <div class="test-container">
        <div class="test-title">Fix 1: Scrollable Chart Tooltip Above Scrollbar</div>
        <div class="test-description">
            Tests that tooltips appear above the scrollbar in scrollable charts, not below it.
        </div>
        <div id="scrollableChart" class="chart-container"></div>
        <div class="instructions">
            <h4>Test Instructions:</h4>
            <ol>
                <li>Scroll the chart horizontally to see the scrollbar</li>
                <li>Hover over columns that are above the scrollbar area</li>
                <li>Verify that the tooltip appears ABOVE the scrollbar, not behind it</li>
                <li>The tooltip should have a very high z-index (9999999)</li>
            </ol>
        </div>
    </div>

    <div class="test-container">
        <div class="test-title">Fix 2: Royalties Line in Front of All Columns</div>
        <div class="test-description">
            Tests that royalties lines appear in front of both main and comparison columns. Previously, comparison columns were appearing in front of royalties lines, which was inconsistent with main columns.
        </div>
        <div id="comparisonChart" class="chart-container"></div>
        <div class="instructions">
            <h4>Test Instructions:</h4>
            <ol>
                <li>Look at the royalties lines (pink/magenta lines)</li>
                <li>Verify that BOTH lines appear IN FRONT of their respective columns:</li>
                <li style="margin-left: 20px;">• Main royalties line should be in front of main columns (left side)</li>
                <li style="margin-left: 20px;">• Comparison royalties line should be in front of comparison columns (right side)</li>
                <li>The lines should not disappear behind any columns</li>
                <li>This creates consistent visual hierarchy across both column types</li>
            </ol>
        </div>
    </div>

    <script src="components/charts/snap-charts.js"></script>
    <script>
        // Generate test data for scrollable chart
        function generateScrollableData() {
            const data = [];
            const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
            
            // Generate 24 months of data to ensure scrolling
            for (let i = 0; i < 24; i++) {
                const monthIndex = i % 12;
                const year = i < 12 ? '24' : '25';
                const sales = Math.floor(Math.random() * 1000) + 200;
                const royalties = Math.floor(sales * 0.3);
                const returns = Math.floor(sales * 0.1);
                
                data.push({
                    month: months[monthIndex],
                    year: year,
                    sales: sales,
                    royalties: royalties,
                    returns: returns,
                    values: [sales],
                    labels: ['Total']
                });
            }
            
            return data;
        }

        // Generate test data for comparison chart
        function generateComparisonData() {
            const currentData = [];
            const comparisonData = [];
            const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN'];
            
            for (let i = 0; i < 6; i++) {
                // Current data
                const currentSales = Math.floor(Math.random() * 800) + 400;
                const currentRoyalties = Math.floor(currentSales * 0.35);
                const currentReturns = Math.floor(currentSales * 0.08);
                
                currentData.push({
                    month: months[i],
                    year: '25',
                    sales: currentSales,
                    royalties: currentRoyalties,
                    returns: currentReturns,
                    values: [currentSales],
                    labels: ['Total']
                });
                
                // Comparison data
                const compSales = Math.floor(Math.random() * 600) + 300;
                const compRoyalties = Math.floor(compSales * 0.32);
                const compReturns = Math.floor(compSales * 0.12);
                
                comparisonData.push({
                    month: months[i],
                    year: '24',
                    sales: compSales,
                    royalties: compRoyalties,
                    returns: compReturns,
                    values: [compSales],
                    labels: ['Total']
                });
            }
            
            return { currentData, comparisonData };
        }

        // Initialize charts
        function initializeCharts() {
            // Scrollable chart
            const scrollableData = generateScrollableData();
            const scrollableChart = new SnapChart({
                container: '#scrollableChart',
                type: 'scrollable-stacked-column',
                data: scrollableData,
                options: {
                    title: 'Scrollable Chart - Tooltip Z-Index Test',
                    animate: true,
                    responsive: true
                }
            });

            // Comparison chart
            const comparisonTestData = generateComparisonData();
            const comparisonChart = new SnapChart({
                container: '#comparisonChart',
                type: 'stacked-column',
                data: comparisonTestData.currentData,
                options: {
                    title: 'Comparison Chart - Royalties Line Order Test',
                    compareMode: true,
                    compareData: comparisonTestData.comparisonData,
                    animate: true,
                    responsive: true
                }
            });
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializeCharts);
    </script>
</body>
</html>
